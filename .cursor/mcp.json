{"mcpServers": {"filesystem": {"command": "bunx", "args": ["@modelcontextprotocol/server-filesystem", "./"]}, "language-server": {"command": "mcp-language-server", "args": ["--workspace", "/Users/<USER>/Projects/debt-tracker", "--lsp", "typescript-language-server", "--", "--st<PERSON>"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--read-only", "--project-ref=gkcjyuwoaeahfredvufc"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}}}
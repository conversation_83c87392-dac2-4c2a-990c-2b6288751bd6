import * as z from "zod/v4";

// Zod schemas for validation
export const createPaymentPlanSchema = z.object({
  name: z.string().min(1, "Plan name is required").max(100),
  strategy: z.enum(["avalanche", "snowball", "custom"]),
  monthlyBudget: z.number().min(0, "Monthly budget must be non-negative"),
  extraPayment: z.number().min(0, "Extra payment must be non-negative"),
  targetDate: z.date().optional(),
  isActive: z.boolean().default(false),
});

export const updatePaymentPlanSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Plan name is required").max(100).optional(),
  strategy: z.enum(["avalanche", "snowball", "custom"]).optional(),
  monthlyBudget: z
    .number()
    .min(0, "Monthly budget must be non-negative")
    .optional(),
  extraPayment: z
    .number()
    .min(0, "Extra payment must be non-negative")
    .optional(),
  targetDate: z.date().optional().nullable(),
  isActive: z.boolean().optional(),
});

export const ZGetPaymentPlanById = z.object({
  id: z.string().uuid(),
});
export type TGetPaymentPlanById = z.infer<typeof ZGetPaymentPlanById>;

export const ZDeletePaymentPlan = z.object({
  id: z.string().uuid(),
});
export type TDeletePaymentPlan = z.infer<typeof ZDeletePaymentPlan>;

export const ZActivatePaymentPlan = z.object({
  id: z.string().uuid(),
});
export type TActivatePaymentPlan = z.infer<typeof ZActivatePaymentPlan>;

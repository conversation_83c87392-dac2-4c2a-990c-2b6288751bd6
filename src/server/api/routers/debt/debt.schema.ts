import * as z from "zod/v4";

export const ZGetDebts = z.object({
  userId: z.string(),
});
export type TGetDebts = z.infer<typeof ZGetDebts>;

// Zod schemas for validation
export const createDebtSchema = z.object({
  name: z.string().min(1, "Debt name is required"),
  type: z.enum(["credit_card", "loan", "mortgage", "other"]),
  balance: z.number().min(0, "Balance must be positive"),
  interestRate: z.number().min(0, "Interest rate must be positive"),
  minimumPayment: z.number().min(0, "Minimum payment must be positive"),
  dueDate: z.date().optional(),
});

export const updateDebtSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Debt name is required").optional(),
  type: z.enum(["credit_card", "loan", "mortgage", "other"]).optional(),
  balance: z.number().min(0, "Balance must be positive").optional(),
  interestRate: z.number().min(0, "Interest rate must be positive").optional(),
  minimumPayment: z
    .number()
    .min(0, "Minimum payment must be positive")
    .optional(),
  dueDate: z.date().optional().nullable(),
});

import type { ProtectedTRPCContext } from "~/server/api/trpc";
import type * as Schema from "./debt.schema";

type Debt<T> = {
  input: T;
  ctx: ProtectedTRPCContext;
};

export async function getDebts({ ctx }: Debt<Schema.TGetDebts>) {
  const { data: user } = await ctx.supabase
    .from("users")
    .select("id")
    .eq("clerk_user_id", ctx.userId)
    .single();

  if (!user) {
    throw new Error("User not found");
  }

  const { data: debts, error } = await ctx.supabase
    .from("debts")
    .select("*")
    .eq("user_id", user.id)
    .order("created_at", { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch debts: ${error.message}`);
  }

  return debts;

  // return debts.map((debt) => ({
  //   id: debt.id,
  //   userId: debt.user_id,
  //   name: debt.name,
  //   type: debt.type as DebtType["type"],
  //   balance: Number(debt.balance),
  //   interestRate: Number(debt.interest_rate),
  //   minimumPayment: Number(debt.minimum_payment),
  //   dueDate: debt.due_date ? new Date(debt.due_date) : null,
  //   createdAt: new Date(debt.created_at || new Date().toISOString()),
  //   updatedAt: new Date(debt.updated_at || new Date().toISOString()),
  // })) as DebtType[];
}
